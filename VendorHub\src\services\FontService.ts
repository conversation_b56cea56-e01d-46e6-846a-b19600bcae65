import { Platform } from 'react-native';
import { useFonts } from 'expo-font';
import { SupportedLanguage } from './I18nService';

export interface FontConfig {
  regular: string;
  medium: string;
  bold: string;
  light?: string;
}

export interface FontFamily {
  en: FontConfig;
  ar: FontConfig;
}

// Font configurations for different languages
export const FONT_FAMILIES: FontFamily = {
  en: {
    regular: Platform.select({
      ios: 'System',
      android: 'Roboto',
      web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    }) as string,
    medium: Platform.select({
      ios: 'System',
      android: 'Roboto-Medium',
      web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    }) as string,
    bold: Platform.select({
      ios: 'System',
      android: 'Roboto-Bold',
      web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    }) as string,
    light: Platform.select({
      ios: 'System',
      android: 'Roboto-Light',
      web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    }) as string,
  },
  ar: {
    // Using system Arabic fonts for now - can be replaced with custom fonts
    regular: Platform.select({
      ios: 'Geeza Pro',
      android: 'Noto Sans Arabic',
      web: 'Tahoma, "Arabic Typesetting", "Times New Roman", serif',
    }) as string,
    medium: Platform.select({
      ios: 'Geeza Pro',
      android: 'Noto Sans Arabic',
      web: 'Tahoma, "Arabic Typesetting", "Times New Roman", serif',
    }) as string,
    bold: Platform.select({
      ios: 'Geeza Pro',
      android: 'Noto Sans Arabic',
      web: 'Tahoma, "Arabic Typesetting", "Times New Roman", serif',
    }) as string,
    light: Platform.select({
      ios: 'Geeza Pro',
      android: 'Noto Sans Arabic',
      web: 'Tahoma, "Arabic Typesetting", "Times New Roman", serif',
    }) as string,
  },
};

// Font loading configuration for custom fonts (if needed)
export const CUSTOM_FONTS = {
  // Add custom Arabic fonts here when available
  // 'Dubai-Regular': require('../../assets/fonts/Dubai-Regular.ttf'),
  // 'Dubai-Medium': require('../../assets/fonts/Dubai-Medium.ttf'),
  // 'Dubai-Bold': require('../../assets/fonts/Dubai-Bold.ttf'),
};

export class FontService {
  private static instance: FontService;

  private constructor() {}

  public static getInstance(): FontService {
    if (!FontService.instance) {
      FontService.instance = new FontService();
    }
    return FontService.instance;
  }

  public getFontFamily(language: SupportedLanguage, weight: keyof FontConfig = 'regular'): string {
    const isArabic = language === 'ar';
    const fontFamily = isArabic ? FONT_FAMILIES.ar : FONT_FAMILIES.en;
    
    return fontFamily[weight] || fontFamily.regular;
  }

  public getTextStyle(language: SupportedLanguage, weight: keyof FontConfig = 'regular') {
    const isArabic = language === 'ar';
    const fontFamily = this.getFontFamily(language, weight);
    
    return {
      fontFamily,
      // Arabic text typically needs more line height
      lineHeight: isArabic ? 1.6 : 1.4,
      // Arabic text direction
      writingDirection: isArabic ? 'rtl' as const : 'ltr' as const,
      textAlign: isArabic ? 'right' as const : 'left' as const,
    };
  }

  public getArabicTextStyle(weight: keyof FontConfig = 'regular') {
    return {
      fontFamily: FONT_FAMILIES.ar[weight] || FONT_FAMILIES.ar.regular,
      lineHeight: 1.6,
      writingDirection: 'rtl' as const,
      textAlign: 'right' as const,
    };
  }

  public getEnglishTextStyle(weight: keyof FontConfig = 'regular') {
    return {
      fontFamily: FONT_FAMILIES.en[weight] || FONT_FAMILIES.en.regular,
      lineHeight: 1.4,
      writingDirection: 'ltr' as const,
      textAlign: 'left' as const,
    };
  }
}

export default FontService.getInstance();
