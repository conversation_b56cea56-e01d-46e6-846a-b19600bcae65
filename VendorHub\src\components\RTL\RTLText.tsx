import React, { useMemo } from 'react';
import { Text, TextProps, StyleSheet, TextStyle } from 'react-native';
import { useI18n } from '../../hooks/useI18n';
import { useFont } from '../../hooks/useFont';

interface RTLTextProps extends TextProps {
  style?: TextStyle | TextStyle[];
  children?: React.ReactNode;
  weight?: 'regular' | 'medium' | 'bold' | 'light';
}

export const RTLText: React.FC<RTLTextProps> = ({ style, children, weight = 'regular', ...props }) => {
  const { isRTL, currentLanguage } = useI18n();

  // Get appropriate font style for current language
  const fontStyle = useFont({ weight });

  const rtlStyle = useMemo(() => {
    const baseStyle = { ...fontStyle };

    if (!style) {
      return baseStyle;
    }

    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...baseStyle, ...flattenedStyle };

    // Flip text alignment if specified and different from default
    if (isRTL && flattenedStyle.textAlign) {
      if (flattenedStyle.textAlign === 'left') {
        rtlFlattenedStyle.textAlign = 'right';
      } else if (flattenedStyle.textAlign === 'right') {
        rtlFlattenedStyle.textAlign = 'left';
      }
    }

    return rtlFlattenedStyle;
  }, [style, isRTL, fontStyle]);

  return <Text style={rtlStyle} {...props}>{children}</Text>;
};
